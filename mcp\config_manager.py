"""
MCP Configuration Manager for BlenderPro

Provides user-friendly configuration management for MCP servers with
validation, templates, and best practices.

Author: inkbytefo
"""

import json
import os
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

from .server_manager import MCPServerConfig, MCPServerManager


@dataclass
class MCPConfigTemplate:
    """Template for common MCP server configurations."""
    name: str
    description: str
    category: str
    config: MCPServerConfig
    required_env_vars: List[str] = None
    setup_instructions: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "config": self.config.to_dict(),
            "required_env_vars": self.required_env_vars or [],
            "setup_instructions": self.setup_instructions
        }


class MCPConfigManager:
    """Advanced configuration manager for MCP servers."""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config_dir = Path(config_dir) if config_dir else self._get_default_config_dir()
        self.config_file = self.config_dir / "mcp_config.json"
        self.templates_file = self.config_dir / "mcp_templates.json"
        
        # Ensure config directory exists
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize templates
        self._create_default_templates()
    
    def _get_default_config_dir(self) -> Path:
        """Get default configuration directory."""
        try:
            import bpy
            user_data_dir = bpy.utils.user_resource('DATAFILES')
            return Path(user_data_dir) / "blenderpro" / "mcp"
        except ImportError:
            # Fallback for non-Blender environments
            return Path.home() / ".blenderpro" / "mcp"
    
    def _create_default_templates(self):
        """Create default MCP server templates."""
        templates = [
            MCPConfigTemplate(
                name="Filesystem Access",
                description="Access local files and directories",
                category="File Management",
                config=MCPServerConfig(
                    name="filesystem",
                    description="Local filesystem access",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-filesystem", "{root_path}"],
                    enabled=False,
                    auto_connect=False
                ),
                setup_instructions="Replace {root_path} with the directory you want to access"
            ),
            MCPConfigTemplate(
                name="Git Repository",
                description="Git repository management and history",
                category="Version Control",
                config=MCPServerConfig(
                    name="git",
                    description="Git repository management",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-git", "--repository", "{repo_path}"],
                    enabled=False,
                    auto_connect=False
                ),
                setup_instructions="Replace {repo_path} with your git repository path"
            ),
            MCPConfigTemplate(
                name="Brave Search",
                description="Web search capabilities",
                category="Web Services",
                config=MCPServerConfig(
                    name="brave-search",
                    description="Brave Search API",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-brave-search"],
                    env={"BRAVE_API_KEY": "{api_key}"},
                    enabled=False,
                    auto_connect=False
                ),
                required_env_vars=["BRAVE_API_KEY"],
                setup_instructions="Get API key from https://api.search.brave.com/app/keys"
            ),
            MCPConfigTemplate(
                name="SQLite Database",
                description="SQLite database access and queries",
                category="Database",
                config=MCPServerConfig(
                    name="sqlite",
                    description="SQLite database access",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-sqlite", "--db-path", "{db_path}"],
                    enabled=False,
                    auto_connect=False
                ),
                setup_instructions="Replace {db_path} with your SQLite database file path"
            ),
            MCPConfigTemplate(
                name="GitHub Integration",
                description="GitHub repository and issue management",
                category="Development",
                config=MCPServerConfig(
                    name="github",
                    description="GitHub API integration",
                    transport_type="stdio",
                    command="npx",
                    args=["-y", "@modelcontextprotocol/server-github"],
                    env={"GITHUB_PERSONAL_ACCESS_TOKEN": "{github_token}"},
                    enabled=False,
                    auto_connect=False
                ),
                required_env_vars=["GITHUB_PERSONAL_ACCESS_TOKEN"],
                setup_instructions="Create a GitHub Personal Access Token at https://github.com/settings/tokens"
            )
        ]
        
        self.save_templates(templates)
    
    def save_templates(self, templates: List[MCPConfigTemplate]):
        """Save configuration templates to file."""
        try:
            template_data = {
                "version": "1.0",
                "templates": [template.to_dict() for template in templates]
            }
            
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"Saved {len(templates)} templates to {self.templates_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save templates: {e}")
    
    def load_templates(self) -> List[MCPConfigTemplate]:
        """Load configuration templates from file."""
        if not self.templates_file.exists():
            self.logger.info("No templates file found, using defaults")
            return []
        
        try:
            with open(self.templates_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            templates = []
            for template_data in data.get("templates", []):
                config_data = template_data["config"]
                config = MCPServerConfig.from_dict(config_data)
                
                template = MCPConfigTemplate(
                    name=template_data["name"],
                    description=template_data["description"],
                    category=template_data["category"],
                    config=config,
                    required_env_vars=template_data.get("required_env_vars", []),
                    setup_instructions=template_data.get("setup_instructions", "")
                )
                templates.append(template)
            
            self.logger.info(f"Loaded {len(templates)} templates")
            return templates
            
        except Exception as e:
            self.logger.error(f"Failed to load templates: {e}")
            return []
    
    def get_templates_by_category(self) -> Dict[str, List[MCPConfigTemplate]]:
        """Get templates organized by category."""
        templates = self.load_templates()
        categories = {}
        
        for template in templates:
            category = template.category
            if category not in categories:
                categories[category] = []
            categories[category].append(template)
        
        return categories
    
    def validate_server_config(self, config: MCPServerConfig) -> List[str]:
        """Validate server configuration and return list of issues."""
        issues = []
        
        # Basic validation
        if not config.name:
            issues.append("Server name is required")
        
        if not config.transport_type:
            issues.append("Transport type is required")
        
        if config.transport_type == "stdio":
            if not config.command:
                issues.append("Command is required for stdio transport")
        elif config.transport_type in ["http", "streamable_http"]:
            if not config.url:
                issues.append("URL is required for HTTP transport")
        
        # Environment variable validation
        if config.env:
            for key, value in config.env.items():
                if not value or value.startswith("{") and value.endswith("}"):
                    issues.append(f"Environment variable {key} needs to be configured")
        
        return issues
    
    def create_server_from_template(self, template_name: str, **kwargs) -> Optional[MCPServerConfig]:
        """Create a server configuration from a template with user parameters."""
        templates = self.load_templates()
        template = next((t for t in templates if t.name == template_name), None)
        
        if not template:
            self.logger.error(f"Template {template_name} not found")
            return None
        
        # Create a copy of the template config
        config_dict = template.config.to_dict()
        
        # Replace placeholders in args
        if config_dict.get("args"):
            config_dict["args"] = [
                arg.format(**kwargs) if isinstance(arg, str) else arg
                for arg in config_dict["args"]
            ]
        
        # Replace placeholders in env vars
        if config_dict.get("env"):
            config_dict["env"] = {
                key: value.format(**kwargs) if isinstance(value, str) else value
                for key, value in config_dict["env"].items()
            }
        
        # Update name to be unique
        if "name" in kwargs:
            config_dict["name"] = kwargs["name"]
        
        return MCPServerConfig.from_dict(config_dict)

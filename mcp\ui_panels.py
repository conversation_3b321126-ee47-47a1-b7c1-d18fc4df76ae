"""
MCP UI Panels for BlenderPro

User-friendly interface for managing MCP server configurations.

Author: inkbytefo
"""

import bpy
from bpy.types import Panel, Operator, PropertyGroup
from bpy.props import StringProperty, BoolProperty, EnumProperty, CollectionProperty

from .config_manager import MCPConfigManager
from .server_manager import MCPServerManager


class MCPServerProperty(PropertyGroup):
    """Property group for MCP server configuration."""
    name: StringProperty(name="Name", description="Server name")
    description: StringProperty(name="Description", description="Server description")
    transport_type: EnumProperty(
        name="Transport",
        description="Transport protocol",
        items=[
            ('stdio', 'STDIO', 'Standard input/output'),
            ('http', 'HTTP', 'HTTP transport'),
            ('streamable_http', 'Streamable HTTP', 'Streamable HTTP transport')
        ],
        default='stdio'
    )
    command: StringProperty(name="Command", description="Command to execute")
    args: StringProperty(name="Arguments", description="Command arguments (space-separated)")
    url: StringProperty(name="URL", description="Server URL (for HTTP transport)")
    enabled: Bool<PERSON>roperty(name="Enabled", description="Enable this server", default=False)
    auto_connect: BoolProperty(name="Auto Connect", description="Connect automatically on startup", default=False)


class BLENDPRO_OT_AddMCPServer(Operator):
    """Add a new MCP server configuration."""
    bl_idname = "blendpro.add_mcp_server"
    bl_label = "Add MCP Server"
    bl_options = {'REGISTER', 'UNDO'}
    
    template_name: StringProperty(name="Template", description="Configuration template to use")
    
    def execute(self, context):
        try:
            config_manager = MCPConfigManager()
            
            # Add new server property
            server_props = context.scene.blendpro_mcp_servers
            new_server = server_props.add()
            new_server.name = f"server_{len(server_props)}"
            new_server.description = "New MCP Server"
            
            self.report({'INFO'}, f"Added new MCP server configuration")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to add server: {e}")
            return {'CANCELLED'}


class BLENDPRO_OT_RemoveMCPServer(Operator):
    """Remove an MCP server configuration."""
    bl_idname = "blendpro.remove_mcp_server"
    bl_label = "Remove MCP Server"
    bl_options = {'REGISTER', 'UNDO'}
    
    index: bpy.props.IntProperty()
    
    def execute(self, context):
        try:
            server_props = context.scene.blendpro_mcp_servers
            if 0 <= self.index < len(server_props):
                server_name = server_props[self.index].name
                server_props.remove(self.index)
                self.report({'INFO'}, f"Removed MCP server: {server_name}")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to remove server: {e}")
            return {'CANCELLED'}


class BLENDPRO_OT_SaveMCPConfig(Operator):
    """Save MCP server configurations."""
    bl_idname = "blendpro.save_mcp_config"
    bl_label = "Save MCP Configuration"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            from .server_manager import MCPServerConfig
            
            server_manager = MCPServerManager()
            server_props = context.scene.blendpro_mcp_servers
            
            # Clear existing servers
            for server_name in list(server_manager.servers.keys()):
                server_manager.remove_server(server_name)
            
            # Add servers from UI
            for server_prop in server_props:
                if server_prop.name:
                    args = server_prop.args.split() if server_prop.args else []
                    
                    config = MCPServerConfig(
                        name=server_prop.name,
                        description=server_prop.description,
                        transport_type=server_prop.transport_type,
                        command=server_prop.command if server_prop.transport_type == 'stdio' else None,
                        args=args if server_prop.transport_type == 'stdio' else None,
                        url=server_prop.url if server_prop.transport_type in ['http', 'streamable_http'] else None,
                        enabled=server_prop.enabled,
                        auto_connect=server_prop.auto_connect
                    )
                    
                    server_manager.add_server(config)
            
            # Save to file
            server_manager.save_server_configs()
            self.report({'INFO'}, f"Saved {len(server_props)} MCP server configurations")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to save configuration: {e}")
            return {'CANCELLED'}


class BLENDPRO_OT_LoadMCPConfig(Operator):
    """Load MCP server configurations."""
    bl_idname = "blendpro.load_mcp_config"
    bl_label = "Load MCP Configuration"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            server_manager = MCPServerManager()
            server_props = context.scene.blendpro_mcp_servers
            
            # Clear existing UI properties
            server_props.clear()
            
            # Load from server manager
            for server_config in server_manager.list_servers():
                server_prop = server_props.add()
                server_prop.name = server_config.name
                server_prop.description = server_config.description
                server_prop.transport_type = server_config.transport_type
                server_prop.command = server_config.command or ""
                server_prop.args = " ".join(server_config.args) if server_config.args else ""
                server_prop.url = server_config.url or ""
                server_prop.enabled = server_config.enabled
                server_prop.auto_connect = server_config.auto_connect
            
            self.report({'INFO'}, f"Loaded {len(server_props)} MCP server configurations")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to load configuration: {e}")
            return {'CANCELLED'}


class BLENDPRO_PT_MCPConfig(Panel):
    """MCP Configuration Panel."""
    bl_label = "MCP Server Configuration"
    bl_idname = "BLENDPRO_PT_mcp_config"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_Panel"
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        # Header with load/save buttons
        row = layout.row(align=True)
        row.operator("blendpro.load_mcp_config", text="Load Config", icon="FILE_FOLDER")
        row.operator("blendpro.save_mcp_config", text="Save Config", icon="FILE_TICK")
        
        layout.separator()
        
        # Add server button
        layout.operator("blendpro.add_mcp_server", text="Add Server", icon="ADD")
        
        layout.separator()
        
        # Server list
        if hasattr(scene, 'blendpro_mcp_servers'):
            servers = scene.blendpro_mcp_servers
            
            for i, server in enumerate(servers):
                box = layout.box()
                
                # Server header
                row = box.row(align=True)
                row.prop(server, "enabled", text="")
                row.prop(server, "name", text="")
                
                # Remove button
                remove_op = row.operator("blendpro.remove_mcp_server", text="", icon="X")
                remove_op.index = i
                
                if server.enabled:
                    # Server details
                    col = box.column(align=True)
                    col.prop(server, "description")
                    col.prop(server, "transport_type")
                    
                    if server.transport_type == 'stdio':
                        col.prop(server, "command")
                        col.prop(server, "args")
                    elif server.transport_type in ['http', 'streamable_http']:
                        col.prop(server, "url")
                    
                    col.prop(server, "auto_connect")


class BLENDPRO_PT_MCPTemplates(Panel):
    """MCP Templates Panel."""
    bl_label = "MCP Server Templates"
    bl_idname = "BLENDPRO_PT_mcp_templates"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_parent_id = "BLENDPRO_PT_mcp_config"
    
    def draw(self, context):
        layout = self.layout
        
        try:
            config_manager = MCPConfigManager()
            templates_by_category = config_manager.get_templates_by_category()
            
            for category, templates in templates_by_category.items():
                box = layout.box()
                box.label(text=category, icon="FOLDER_REDIRECT")
                
                for template in templates:
                    row = box.row()
                    row.label(text=template.name, icon="SETTINGS")
                    
                    # Add template button
                    add_op = row.operator("blendpro.add_mcp_server", text="Add")
                    add_op.template_name = template.name
                    
                    # Show description
                    if template.description:
                        sub_row = box.row()
                        sub_row.label(text=f"  {template.description}", icon="INFO")
                    
                    # Show setup instructions
                    if template.setup_instructions:
                        sub_row = box.row()
                        sub_row.label(text=f"  Setup: {template.setup_instructions}", icon="QUESTION")
        
        except Exception as e:
            layout.label(text=f"Error loading templates: {e}", icon="ERROR")


# Registration
classes = [
    MCPServerProperty,
    BLENDPRO_OT_AddMCPServer,
    BLENDPRO_OT_RemoveMCPServer,
    BLENDPRO_OT_SaveMCPConfig,
    BLENDPRO_OT_LoadMCPConfig,
    BLENDPRO_PT_MCPConfig,
    BLENDPRO_PT_MCPTemplates,
]


def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Add collection property to scene
    bpy.types.Scene.blendpro_mcp_servers = CollectionProperty(type=MCPServerProperty)


def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    
    # Remove collection property
    if hasattr(bpy.types.Scene, 'blendpro_mcp_servers'):
        del bpy.types.Scene.blendpro_mcp_servers

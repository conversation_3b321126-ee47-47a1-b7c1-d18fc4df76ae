#!/usr/bin/env python3
"""
Test script for MCP integration in BlenderPro.

This script tests the MCP functionality without requiring <PERSON><PERSON>der to be running.
It can be used to verify that the MCP integration is working correctly.

Author: inkbytefo
"""

import sys
import os
import asyncio
import logging

# Add the lib directory to Python path for MCP SDK imports
lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lib')
if lib_path not in sys.path:
    sys.path.insert(0, lib_path)

# Add the current directory to Python path for our modules
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_mcp_imports():
    """Test that MCP modules can be imported correctly."""
    print("Testing MCP imports...")
    
    try:
        from mcp.client import MCPClientManager
        from mcp.transport import MCPTransportManager
        from mcp.server_manager import MCPServerManager
        print("✓ MCP modules imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import MCP modules: {e}")
        return False


def test_mcp_sdk_availability():
    """Test that the MCP Python SDK is available."""
    print("Testing MCP SDK availability...")

    try:
        # Add lib directory to path to find the installed MCP package
        lib_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lib')
        if lib_path not in sys.path:
            sys.path.insert(0, lib_path)

        # Import from the installed MCP package in lib
        import mcp as mcp_sdk
        from mcp import ClientSession
        print(f"✓ MCP SDK available (version: {getattr(mcp_sdk, '__version__', 'unknown')})")
        return True
    except ImportError as e:
        print(f"✗ MCP SDK not available: {e}")
        print("  Install with: pip install mcp>=1.11.0")
        return False


def test_server_manager():
    """Test the MCP server manager functionality."""
    print("Testing MCP server manager...")
    
    try:
        from mcp.server_manager import MCPServerManager, MCPServerConfig
        
        # Create a temporary config directory
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = MCPServerManager(config_dir=temp_dir)
            
            # Test adding a server
            test_server = MCPServerConfig(
                name="test-server",
                description="Test server for validation",
                transport_type="stdio",
                command="echo",
                args=["hello"],
                enabled=False
            )
            
            success = manager.add_server(test_server)
            if not success:
                print("✗ Failed to add test server")
                return False
            
            # Test listing servers
            servers = manager.list_servers()
            if len(servers) == 0:
                print("✗ No servers found after adding")
                return False
            
            # Test saving/loading config
            save_success = manager.save_server_configs()
            if not save_success:
                print("✗ Failed to save server configs")
                return False
            
            print("✓ Server manager functionality working")
            return True
            
    except Exception as e:
        print(f"✗ Server manager test failed: {e}")
        return False


def test_transport_manager():
    """Test the MCP transport manager functionality."""
    print("Testing MCP transport manager...")
    
    try:
        from mcp.transport import MCPTransportManager
        
        manager = MCPTransportManager()
        
        # Test MCP availability check
        is_available = manager.is_mcp_available()
        print(f"  MCP SDK available: {is_available}")
        
        # Test connection tracking
        connections = manager.get_active_connections()
        print(f"  Active connections: {len(connections)}")
        
        print("✓ Transport manager functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Transport manager test failed: {e}")
        return False


async def test_client_manager():
    """Test the MCP client manager functionality."""
    print("Testing MCP client manager...")
    
    try:
        from mcp.client import MCPClientManager
        
        manager = MCPClientManager()
        
        # Test initialization
        is_available = manager.is_mcp_available()
        print(f"  MCP available: {is_available}")
        
        if is_available:
            # Test initialization
            init_success = await manager.initialize()
            print(f"  Initialization: {'✓' if init_success else '✗'}")
            
            # Test server status
            status = manager.get_server_status_summary()
            print(f"  Server status summary: {status}")
            
            # Test shutdown
            await manager.shutdown()
            print("  Shutdown completed")
        
        print("✓ Client manager functionality working")
        return True
        
    except Exception as e:
        print(f"✗ Client manager test failed: {e}")
        return False


async def main():
    """Run all MCP integration tests."""
    print("=== BlenderPro MCP Integration Test ===\n")
    
    tests = [
        ("MCP Imports", test_mcp_imports),
        ("MCP SDK Availability", test_mcp_sdk_availability),
        ("Server Manager", test_server_manager),
        ("Transport Manager", test_transport_manager),
        ("Client Manager", test_client_manager),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n=== Test Summary ===")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! MCP integration is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
